<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ isEditMode ? 'Edit Partner' : 'Partner Details' }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ isEditMode ? 'Update partner information and settings' : 'View partner information and manage settings'
            }}
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="goBack"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <ArrowLeftIcon class="w-4 h-4 mr-2" />
            Back to Partners
          </button>
          <button v-if="!isEditMode && canEditPartner" @click="toggleEditMode"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <PencilIcon class="w-4 h-4 mr-2" />
            Edit Partner
          </button>
          <button v-if="isEditMode" @click="saveChanges" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 disabled:opacity-50">
            <CheckIcon class="w-4 h-4 mr-2" />
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </button>
          <button v-if="isEditMode" @click="cancelEdit"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <XMarkIcon class="w-4 h-4 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2 text-gray-500">
        <svg class="animate-spin h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span>Loading partner details...</span>
      </div>
    </div>

    <!-- Partner Information -->
    <div v-else-if="partner" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Partner Information</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name</label>
          <input v-if="isEditMode" v-model="partnerForm.name" type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.name || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input v-if="isEditMode" v-model="partnerForm.email_address" type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.email_address || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input v-if="isEditMode" v-model="partnerForm.msisdn" type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.msisdn || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select v-if="isEditMode" v-model="partnerForm.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option :value="1">Active</option>
            <option :value="0">Inactive</option>
          </select>
          <span v-else :class="{
            'bg-green-100 text-green-800': partner.status === 1,
            'bg-red-100 text-red-800': partner.status === 0
          }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
            {{ partner.status === 1 ? 'Active' : 'Inactive' }}
          </span>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
          <input v-if="isEditMode" v-model="partnerForm.address"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.address || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
          <p class="text-gray-900">{{ formatDate(partner.created_at) }}</p>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div v-if="partner && !initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
              activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <component :is="tab.icon" class="w-4 h-4 mr-2 inline" />
            {{ tab.label }}
            <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
              {{ tab.count }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Settings Tab -->
        <div v-if="activeTab === 'settings'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Partner Settings</h3>
            <button @click="manageSettings"
              class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <Cog6ToothIcon class="w-4 h-4 mr-2" />
              Manage Settings
            </button>
          </div>

          <div v-if="partnerSettings" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Important Settings - Always Visible -->
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">API Key</p>
              <p class="text-sm text-gray-900 font-mono">{{ maskApiKey(partnerSettings.api_key) }}</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">Currency</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.currency || '-' }}</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">Billing Mode</p>
              <p class="text-sm text-gray-900 capitalize">{{ partnerSettings.billing_mode || '-' }}</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">Rate Limit</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.rate_limit || '-' }}/min</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">Timezone</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.timezone || '-' }}</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700 mb-1">Status</p>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
              </span>
            </div>
          </div>

          <div v-if="partnerSettings" class="mt-6 flex justify-center">
            <button @click="showSettingsModal = true"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All Details
            </button>
          </div>

          <div v-else class="text-center py-12">
            <Cog6ToothIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">No settings configured</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by configuring partner settings.</p>
            <div class="mt-6">
              <button @click="manageSettings"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Cog6ToothIcon class="w-4 h-4 mr-2" />
                Configure Settings
              </button>
            </div>
          </div>
        </div>

        <!-- Services Tab -->
        <div v-else-if="activeTab === 'services'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Partner Services</h3>
            <button @click="manageServices"
              class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <Cog6ToothIcon class="w-4 h-4 mr-2" />
              Manage Services
            </button>
          </div>

          <div v-if="partnerServices && partnerServices.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="service in partnerServices.slice(0, 6)" :key="service.id"
              class="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900">{{ service.service_name || 'Unknown Service' }}</h4>
                <span
                  :class="{
                    'bg-green-100 text-green-800': service.status === 'active' || service.status === 1,
                    'bg-red-100 text-red-800': service.status === 'inactive' || service.status === 0
                  }"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium">
                  {{ service.status === 'active' || service.status === 1 ? 'Active' : 'Inactive' }}
                </span>
              </div>
              <div class="space-y-1 text-xs text-gray-500">
                <p>Rate Limit: {{ service.rate_limit_per_minute || 0 }}/min</p>
                <p>Created: {{ formatDate(service.created_at) }}</p>
              </div>
            </div>
          </div>

          <div v-if="partnerServices && partnerServices.length > 6" class="mt-6 flex justify-center">
            <button @click="showServicesModal = true"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All {{ partnerServices.length }} Services
            </button>
          </div>

          <div v-else-if="!partnerServices || partnerServices.length === 0" class="text-center py-12">
            <Cog6ToothIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">No services configured</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by adding services for this partner.</p>
            <div class="mt-6">
              <button @click="manageServices"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Cog6ToothIcon class="w-4 h-4 mr-2" />
                Add Service
              </button>
            </div>
          </div>
        </div>

        <!-- Bets Tab -->
        <div v-else-if="activeTab === 'bets'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Partner Bets</h3>
            <button @click="viewAllBets"
              class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All Bets
            </button>
          </div>

          <div v-if="partnerBets && partnerBets.length > 0" class="space-y-4">
            <div v-for="bet in partnerBets.slice(0, 5)" :key="bet.id"
              class="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
              <div class="flex items-center justify-between mb-2">
                <div>
                  <p class="text-sm font-medium text-gray-900">Bet #{{ bet.bet_reference || bet.id }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(bet.created_at) }}</p>
                </div>
                <span
                  :class="{
                    'bg-yellow-100 text-yellow-800': bet.status === 'pending',
                    'bg-green-100 text-green-800': bet.status === 'won',
                    'bg-red-100 text-red-800': bet.status === 'lost',
                    'bg-gray-100 text-gray-800': bet.status === 'cancelled'
                  }"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium">
                  {{ bet.status }}
                </span>
              </div>
              <div class="grid grid-cols-2 gap-4 text-xs text-gray-500">
                <div>
                  <span class="font-medium">Amount:</span> {{ formatCurrency(bet.bet_amount) }}
                </div>
                <div>
                  <span class="font-medium">Potential Win:</span> {{ formatCurrency(bet.potential_win) }}
                </div>
              </div>
            </div>
          </div>

          <div v-if="partnerBets && partnerBets.length > 5" class="mt-6 flex justify-center">
            <button @click="viewAllBets"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All {{ partnerBets.length }} Bets
            </button>
          </div>

          <div v-else-if="!partnerBets || partnerBets.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No bets found</h3>
            <p class="mt-1 text-sm text-gray-500">This partner hasn't placed any bets yet.</p>
          </div>
        </div>

        <!-- Users Tab -->
        <div v-else-if="activeTab === 'users'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Partner Users</h3>
            <button @click="viewAllUsers"
              class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All Users
            </button>
          </div>

          <div v-if="partnerUsers && partnerUsers.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="user in partnerUsers.slice(0, 6)" :key="user.id"
              class="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900">{{ user.username || user.email }}</h4>
                <span
                  :class="{
                    'bg-green-100 text-green-800': user.status === 'active' || user.status === 1,
                    'bg-red-100 text-red-800': user.status === 'inactive' || user.status === 0
                  }"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium">
                  {{ user.status === 'active' || user.status === 1 ? 'Active' : 'Inactive' }}
                </span>
              </div>
              <div class="space-y-1 text-xs text-gray-500">
                <p>Email: {{ user.email || '-' }}</p>
                <p>Joined: {{ formatDate(user.created_at) }}</p>
              </div>
            </div>
          </div>

          <div v-if="partnerUsers && partnerUsers.length > 6" class="mt-6 flex justify-center">
            <button @click="viewAllUsers"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <EyeIcon class="w-4 h-4 mr-2" />
              View All {{ partnerUsers.length }} Users
            </button>
          </div>

          <div v-else-if="!partnerUsers || partnerUsers.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
            <p class="mt-1 text-sm text-gray-500">This partner doesn't have any users yet.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="!initialLoading && !partner" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Partner not found</h3>
      <p class="mt-1 text-sm text-gray-500">The partner you're looking for doesn't exist or has been removed.</p>
    </div>

    <!-- Settings Detail Modal -->
    <PartnerSettingsDetailModal
      :is-open="showSettingsModal"
      :settings="partnerSettings"
      @close="showSettingsModal = false"
    />

    <!-- Services Detail Modal -->
    <PartnerServicesDetailModal
      :is-open="showServicesModal"
      :services="partnerServices"
      @close="showServicesModal = false"
      @view-service="handleViewService"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  Cog6ToothIcon,
  EyeIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'
import { partnerApi, type Partner, type PartnerSettings } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'
import { formatDate, formatCurrency } from '@/utils/formatters'
import { getNavigationData, storeNavigationData } from '@/utils/navigationCache'
import PartnerSettingsDetailModal from '@/components/Modals/PartnerSettingsDetailModal.vue'
import PartnerServicesDetailModal from '@/components/Modals/PartnerServicesDetailModal.vue'

// Router and stores
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const partner = ref<Partner | null>(null)
const partnerSettings = ref<PartnerSettings | null>(null)
const partnerServices = ref<any[]>([])
const partnerBets = ref<any[]>([])
const partnerUsers = ref<any[]>([])

// Tab management
const activeTab = ref('settings')
const showSettingsModal = ref(false)
const showServicesModal = ref(false)

// Tab configuration
const tabs = computed(() => [
  {
    key: 'settings',
    label: 'Settings',
    icon: Cog6ToothIcon,
    count: partnerSettings.value ? 1 : 0
  },
  {
    key: 'services',
    label: 'Services',
    icon: ChartBarIcon,
    count: partnerServices.value?.length || 0
  },
  {
    key: 'bets',
    label: 'Bets',
    icon: ChartBarIcon,
    count: partnerBets.value?.length || 0
  },
  {
    key: 'users',
    label: 'Users',
    icon: UserGroupIcon,
    count: partnerUsers.value?.length || 0
  }
])

// Edit mode
const isEditMode = computed(() => route.query.mode === 'edit')

// Permissions
const canEditPartner = computed(() => {
  return authStore.hasAnyRole([1, 2]) || authStore.isSuperUser
})

// Partner form for editing
const partnerForm = reactive({
  name: '',
  address: '',
  email_address: '',
  msisdn: '',
  status: 1
})



// Methods
const fetchPartner = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) {
    router.push({ name: 'partners' })
    return
  }

  // First try to get cached data
  const cachedPartner = getNavigationData('partner', partnerId)

  console.log('Route partner ID:', partnerId)
  console.log('Cached partner found:', !!cachedPartner)

  if (cachedPartner) {
    partner.value = cachedPartner
    populateForm(cachedPartner)
    console.log('Using cached partner data for ID:', JSON.stringify(partner.value))
    return
  }

  // If no cached data, fetch from API
  try {
    console.log('Fetching partner from API for ID:', partnerId)
    const response = await partnerApi.getPartners({
      partner_id: partnerId,
      limit: 1
    })

    console.log('Partner API response status:', JSON.stringify(response))

    if (response.status === 200 && response.message.data && response.message.data.length > 0) {
      partner.value = response.message.data[0]

      // Store for future use
      storeNavigationData(partner.value, 'partner')
      populateForm(partner.value)
      console.log('Partner loaded from API')
    } else {
      console.log('No partner found in API response')
      partner.value = null
    }
  } catch (error) {
    console.error('Error fetching partner:', error)
    partner.value = null
  }
}

const populateForm = (partnerData: any) => {
  if (partnerData) {
    partnerForm.name = partnerData.name || ''
    partnerForm.address = partnerData.address || ''
    partnerForm.email_address = partnerData.email_address || ''
    partnerForm.msisdn = partnerData.msisdn || ''
    partnerForm.status = partnerData.status || 1
  }
}

const fetchPartnerSettings = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerSettings(partnerId)

    console.log('Partner Setting API XX:', JSON.stringify(response))

    if (response.status === 200) {
      partnerSettings.value = response.message
      console.log('Partner Setting loaded from API')
    } else {
      console.log('No partner setting found in API response')
      partnerSettings.value = null
    }
    // partnerSettings.value = null
  } catch (error) {
    console.error('Error fetching partner settings:', error)
  }
}

const fetchPartnerServices = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerServices(partnerId)
    if (response.status === 200) {
      partnerServices.value = response.message.data || []
    } else {
      console.log('No partner setting found in API response')
      partnerServices.value = []
    }

  } catch (error) {
    console.error('Error fetching partner services:', error)
  }
}

const toggleEditMode = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id },
    query: { mode: 'edit' }
  })
}

const cancelEdit = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id }
  })
}

const saveChanges = async () => {
  loading.value = true
  try {
    // This would need an update partner API endpoint
    // For now, just navigate back to view mode
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call

    router.push({
      name: 'partner-details',
      params: { id: route.params.id }
    })
  } catch (error) {
    console.error('Error saving partner:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'partners' })
}

const manageServices = () => {
  router.push({
    name: 'partner-services',
    query: { partner_id: route.params.id }
  })
}

const manageSettings = () => {
  // Navigate to partner settings page or open modal
  console.log('Manage settings for partner:', route.params.id)
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '-'
  if (apiKey.length <= 8) return apiKey
  return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
}

// Additional methods for tabs
const viewAllBets = () => {
  router.push({
    name: 'partners-bets',
    query: { partner_id: route.params.id }
  })
}

const viewAllUsers = () => {
  // Navigate to partner users page when available
  console.log('View all users for partner:', route.params.id)
}

const handleViewService = (service: any) => {
  // Handle viewing individual service details
  console.log('View service details:', service)
  // Could navigate to service details page or open another modal
}

const fetchPartnerBets = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerBets({ partner_id: partnerId, limit: 10 })
    if (response.status === 200) {
      partnerBets.value = response.message.data || []
    } else {
      partnerBets.value = []
    }
  } catch (error) {
    console.error('Error fetching partner bets:', error)
    partnerBets.value = []
  }
}

const fetchPartnerUsers = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    // This would be implemented when the API endpoint is available
    // const response = await partnerApi.getPartnerUsers({ partner_id: partnerId, limit: 10 })
    // if (response.status === 200) {
    //   partnerUsers.value = response.message.data || []
    // } else {
    //   partnerUsers.value = []
    // }
    partnerUsers.value = [] // Placeholder until API is available
  } catch (error) {
    console.error('Error fetching partner users:', error)
    partnerUsers.value = []
  }
}

// Lifecycle
onMounted(async () => {
  console.log('PartnerDetails mounted, route params:', route.params)

  await Promise.all([
    fetchPartner(),
    fetchPartnerSettings(),
    fetchPartnerServices(),
    fetchPartnerBets(),
    fetchPartnerUsers()
  ])
  initialLoading.value = false

  console.log('Partner loading completed')
})
</script>
