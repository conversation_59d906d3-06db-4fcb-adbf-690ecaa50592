<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ isEditMode ? 'Edit Partner' : 'Partner Details' }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ isEditMode ? 'Update partner information and settings' : 'View partner information and manage settings'
            }}
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="goBack"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <ArrowLeftIcon class="w-4 h-4 mr-2" />
            Back to Partners
          </button>
          <button v-if="!isEditMode && canEditPartner" @click="toggleEditMode"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <PencilIcon class="w-4 h-4 mr-2" />
            Edit Partner
          </button>
          <button v-if="isEditMode" @click="saveChanges" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 disabled:opacity-50">
            <CheckIcon class="w-4 h-4 mr-2" />
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </button>
          <button v-if="isEditMode" @click="cancelEdit"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <XMarkIcon class="w-4 h-4 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2 text-gray-500">
        <svg class="animate-spin h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span>Loading partner details...</span>
      </div>
    </div>

    <!-- Partner Information -->
    <div v-else-if="partner" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Partner Information</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name</label>
          <input v-if="isEditMode" v-model="partnerForm.name" type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.name || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input v-if="isEditMode" v-model="partnerForm.email_address" type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.email_address || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input v-if="isEditMode" v-model="partnerForm.msisdn" type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.msisdn || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select v-if="isEditMode" v-model="partnerForm.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option :value="1">Active</option>
            <option :value="0">Inactive</option>
          </select>
          <span v-else :class="{
            'bg-green-100 text-green-800': partner.status === 1,
            'bg-red-100 text-red-800': partner.status === 0
          }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
            {{ partner.status === 1 ? 'Active' : 'Inactive' }}
          </span>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
          <input v-if="isEditMode" v-model="partnerForm.address"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
          <p v-else class="text-gray-900">{{ partner.address || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
          <p class="text-gray-900">{{ formatDate(partner.created_at) }}</p>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div v-if="partner && !initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
              activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <component :is="tab.icon" class="w-4 h-4 mr-2 inline" />
            {{ tab.label }}
            <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
              {{ tab.count }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Settings Tab -->
        <div v-if="activeTab === 'settings'">
          <DataTable
            :data="partnerSettings"
            :headers="settingsHeaders"
            title="Partner Settings"
            :loading="loading"
            :searchable="false"
            :pagination="false"
            :has-actions="true"
            empty-message="No settings configured"
            @row-click="handleSettingsRowClick"
          >
            <!-- Header Actions -->
            <template #header-actions>
              <button @click="manageSettings"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <Cog6ToothIcon class="w-4 h-4 mr-2" />
                Manage Settings
              </button>
            </template>

            <!-- Custom API Key Cell -->
            <template #cell-api_key="{ value }">
              <span class="font-mono text-sm">{{ maskApiKey(value) }}</span>
            </template>

            <!-- Custom Billing Mode Cell -->
            <template #cell-billing_mode="{ value }">
              <span class="capitalize">{{ value || '-' }}</span>
            </template>

            <!-- Custom Rate Limit Cell -->
            <template #cell-rate_limit="{ value }">
              <span>{{ value || '-' }}</span>
            </template>


            <!-- Custom Rate Limit Cell -->
            <!-- <template #cell-version="{ value }">
              <span>{{ value || '-' }}</span>
            </template> -->

            <!-- Custom Websites Cell -->
            <template #cell-websites="{ value }">
              <div v-if="parseWebsites(value).length > 0" class="space-y-1">
                <div v-for="(website, index) in parseWebsites(value).slice(0, 2)" :key="index" class="text-sm">
                  {{ website }}
                </div>
                <div v-if="parseWebsites(value).length > 2" class="text-xs text-gray-500">
                  +{{ parseWebsites(value).length - 2 }} more
                </div>
              </div>
              <span v-else class="text-gray-500">-</span>
            </template>

            <!-- Custom Status Cell -->
            <template #cell-status>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
              </span>
            </template>
          </DataTable>
        </div>

        <!-- Services Tab -->
        <div v-else-if="activeTab === 'services'">
          <DataTable
            :data="partnerServices"
            :headers="servicesHeaders"
            title="Partner Services"
            :loading="loading"
            :searchable="true"
            :pagination="false"
            :has-actions="true"
            empty-message="No services configured"
            @row-click="handleServiceRowClick"
          >
            <!-- Header Actions -->
            <template #header-actions>
              <button @click="manageServices"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <Cog6ToothIcon class="w-4 h-4 mr-2" />
                Manage Services
              </button>
            </template>

            <!-- Custom Service Name Cell -->
            <template #cell-service_name="{ value }">
              <span class="font-medium text-gray-900">{{ value || 'Unknown Service' }}</span>
            </template>

            <!-- Custom Status Cell -->
            <template #cell-status="{ value }">
              <span
                :class="{
                  'bg-green-100 text-green-800': value === 'active' || value === 1,
                  'bg-red-100 text-red-800': value === 'inactive' || value === 0,
                  'bg-yellow-100 text-yellow-800': value === 'pending'
                }"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getServiceStatusText(value) }}
              </span>
            </template>

            <!-- Custom Rate Limit Cell -->
            <template #cell-rate_limit_per_minute="{ value }">
              <span class="font-medium">{{ value || '0' }}/min</span>
            </template>

            <!-- Custom Partner Name Cell -->
            <template #cell-partner_name="{ value }">
              <span class="text-gray-900">{{ value || '-' }}</span>
            </template>

            <!-- Custom Service Count Cell -->
            <template #cell-service_count="{ value }">
              <span class="font-medium text-blue-600">{{ value || '0' }}</span>
            </template>
          </DataTable>
        </div>

        <!-- Bets Tab -->
        <div v-else-if="activeTab === 'bets'">
          <DataTable
            :data="partnerBets"
            :headers="betsHeaders"
            title="Partner Bets"
            :loading="loading"
            :searchable="true"
            :pagination="true"
            :current-page="1"
            :total-records="partnerBets?.length || 0"
            :page-size="10"
            :has-actions="true"
            empty-message="No bets found"
            @row-click="handleBetRowClick"
          >
            <!-- Header Actions -->
            <template #header-actions>
              <button @click="viewAllBets"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <EyeIcon class="w-4 h-4 mr-2" />
                View All Bets
              </button>
            </template>

            <!-- Custom Bet Reference Cell -->
            <template #cell-bet_reference="{ value, item }">
              <div class="text-sm">
                <div class="font-medium text-gray-900">{{ value || `Bet #${item.id}` }}</div>
                <div class="text-gray-500">ID: {{ item.id }}</div>
              </div>
            </template>

            <!-- Custom Status Cell -->
            <template #cell-status="{ value }">
              <span
                :class="{
                  'bg-yellow-100 text-yellow-800': value === 'pending',
                  'bg-green-100 text-green-800': value === 'won',
                  'bg-red-100 text-red-800': value === 'lost',
                  'bg-gray-100 text-gray-800': value === 'cancelled'
                }"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize">
                {{ value }}
              </span>
            </template>

            <!-- Custom Bet Amount Cell -->
            <template #cell-bet_amount="{ value }">
              <span class="font-medium text-gray-900">{{ formatCurrency(value) }}</span>
            </template>

            <!-- Custom Potential Win Cell -->
            <template #cell-potential_win="{ value }">
              <span class="font-medium text-green-600">{{ formatCurrency(value) }}</span>
            </template>

            <!-- Custom User ID Cell -->
            <template #cell-user_id="{ value }">
              <span class="text-gray-600">{{ value || '-' }}</span>
            </template>
          </DataTable>
        </div>

        <!-- Users Tab -->
        <div v-else-if="activeTab === 'users'">
          <DataTable
            :data="partnerUsers"
            :headers="usersHeaders"
            title="Partner Users"
            :loading="loading"
            :searchable="true"
            :pagination="true"
            :current-page="1"
            :total-records="partnerUsers?.length || 0"
            :page-size="10"
            :has-actions="true"
            empty-message="No users found"
            @row-click="handleUserRowClick"
          >
            <!-- Header Actions -->
            <template #header-actions>
              <button @click="viewAllUsers"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <EyeIcon class="w-4 h-4 mr-2" />
                View All Users
              </button>
            </template>

            <!-- Custom Username Cell -->
            <template #cell-username="{ value, item }">
              <div class="text-sm">
                <div class="font-medium text-gray-900">{{ value || item.email || 'Unknown User' }}</div>
                <div class="text-gray-500">ID: {{ item.id }}</div>
              </div>
            </template>

            <!-- Custom Email Cell -->
            <template #cell-email="{ value }">
              <span class="text-gray-900">{{ value || '-' }}</span>
            </template>

            <!-- Custom Status Cell -->
            <template #cell-status="{ value }">
              <span
                :class="{
                  'bg-green-100 text-green-800': value === 'active' || value === 1,
                  'bg-red-100 text-red-800': value === 'inactive' || value === 0,
                  'bg-yellow-100 text-yellow-800': value === 'pending'
                }"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getUserStatusText(value) }}
              </span>
            </template>

            <!-- Custom Phone Cell -->
            <template #cell-phone="{ value }">
              <span class="text-gray-600">{{ value || '-' }}</span>
            </template>

            <!-- Custom Role Cell -->
            <template #cell-role="{ value }">
              <span class="text-gray-600 capitalize">{{ value || 'User' }}</span>
            </template>
          </DataTable>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="!initialLoading && !partner" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Partner not found</h3>
      <p class="mt-1 text-sm text-gray-500">The partner you're looking for doesn't exist or has been removed.</p>
    </div>

    <!-- Settings Detail Modal -->
    <PartnerSettingsDetailModal
      :is-open="showSettingsModal"
      :settings="partnerSettings"
      @close="showSettingsModal = false"
    />

    <!-- Services Detail Modal -->
    <PartnerServicesDetailModal
      :is-open="showServicesModal"
      :services="partnerServices"
      @close="showServicesModal = false"
      @view-service="handleViewService"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  Cog6ToothIcon,
  EyeIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'
import { partnerApi, type Partner, type PartnerSettings } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'
import { formatDate, formatCurrency } from '@/utils/formatters'
import { getNavigationData, storeNavigationData } from '@/utils/navigationCache'
import PartnerSettingsDetailModal from '@/components/Modals/PartnerSettingsDetailModal.vue'
import PartnerServicesDetailModal from '@/components/Modals/PartnerServicesDetailModal.vue'
import DataTable from '@/components/DataTable.vue'

// Router and stores
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const partner = ref<any | null>(null)
const partnerSettings = ref<any | null>(null)
const partnerServices = ref<any[]>([])
const partnerBets = ref<any[]>([])
const partnerUsers = ref<any[]>([])

// Tab management
const activeTab = ref('settings')
const showSettingsModal = ref(false)
const showServicesModal = ref(false)

// Tab configuration
const tabs = computed(() => [
  {
    key: 'settings',
    label: 'Settings',
    icon: Cog6ToothIcon,
    count: partnerSettings.value ? 1 : 0
  },
  {
    key: 'services',
    label: 'Services',
    icon: ChartBarIcon,
    count: partnerServices.value?.length || 0
  },
  {
    key: 'bets',
    label: 'Bets',
    icon: ChartBarIcon,
    count: partnerBets.value?.length || 0
  },
  {
    key: 'users',
    label: 'Users',
    icon: UserGroupIcon,
    count: partnerUsers.value?.length || 0
  }
])

// Table headers
const settingsHeaders = computed(() => ({
  // setting_id: 'Setting ID',
  partner_name: 'Partner Name',
  api_key: 'API Key',
  currency: 'Currency',
  denomination: 'Denomination',
  billing_mode: 'Billing Mode',
  rate_limit: 'Rate Limit',
  timezone: 'Timezone',
  ip_address: 'IP Address',
  callback_url: 'Callback URL',
  websites: 'Websites',
  version: 'Version',
  status: 'Status',
  created_at: 'Created At',
  updated_at: 'Updated At'
}))

const servicesHeaders = computed(() => ({
  id: 'Service ID',
  service_name: 'Service Name',
  partner_name: 'Partner Name',
  status: 'Status',
  rate_limit_per_minute: 'Rate Limit',
  service_count: 'Service Count',
  created_at: 'Created At'
}))

const betsHeaders = computed(() => ({
  bet_reference: 'Bet Reference',
  status: 'Status',
  bet_amount: 'Bet Amount',
  potential_win: 'Potential Win',
  user_id: 'User ID',
  created_at: 'Created At'
}))

const usersHeaders = computed(() => ({
  username: 'Username',
  email: 'Email',
  status: 'Status',
  phone: 'Phone',
  role: 'Role',
  created_at: 'Joined Date'
}))

// Edit mode
const isEditMode = computed(() => route.query.mode === 'edit')

// Permissions
const canEditPartner = computed(() => {
  return authStore.hasAnyRole([1, 2]) || authStore.isSuperUser
})

// Partner form for editing
const partnerForm = reactive({
  name: '',
  address: '',
  email_address: '',
  msisdn: '',
  status: 1
})



// Methods
const fetchPartner = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) {
    router.push({ name: 'partners' })
    return
  }

  // First try to get cached data
  const cachedPartner = getNavigationData('partner', partnerId)

  console.log('Route partner ID:', partnerId)
  console.log('Cached partner found:', !!cachedPartner)

  if (cachedPartner) {
    partner.value = cachedPartner
    populateForm(cachedPartner)
    console.log('Using cached partner data for ID:', JSON.stringify(partner.value))
    return
  }

  // If no cached data, fetch from API
  try {
    console.log('Fetching partner from API for ID:', partnerId)
    const response = await partnerApi.getPartners({
      partner_id: partnerId,
      limit: 1
    })

    console.log('Partner API response status:', JSON.stringify(response))

    if (response.status === 200 && response.message.data && response.message.data.length > 0) {
      partner.value = response.message.data[0]

      // Store for future use
      storeNavigationData(partner.value, 'partner')
      populateForm(partner.value)
      console.log('Partner loaded from API')
    } else {
      console.log('No partner found in API response')
      partner.value = null
    }
  } catch (error) {
    console.error('Error fetching partner:', error)
    partner.value = null
  }
}

const populateForm = (partnerData: any) => {
  if (partnerData) {
    partnerForm.name = partnerData.name || ''
    partnerForm.address = partnerData.address || ''
    partnerForm.email_address = partnerData.email_address || ''
    partnerForm.msisdn = partnerData.msisdn || ''
    partnerForm.status = partnerData.status || 1
  }
}

const fetchPartnerSettings = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerSettings(partnerId)

    // console.log('Partner Setting API XX:', JSON.stringify(response))

    if (response.status === 200) {
      partnerSettings.value = response.message.result
      console.log('Partner Setting loaded from API>>>',JSON.stringify(partnerSettings.value))
    } else {
      console.log('No partner setting found in API response')
      partnerSettings.value = null
    }
    // partnerSettings.value = null
  } catch (error) {
    console.error('Error fetching partner settings:', error)
  }
}

const fetchPartnerServices = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerServices(partnerId)
    if (response.status === 200) {
      partnerServices.value = response.message.data || []
    } else {
      console.log('No partner setting found in API response')
      partnerServices.value = []
    }

  } catch (error) {
    console.error('Error fetching partner services:', error)
  }
}

const toggleEditMode = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id },
    query: { mode: 'edit' }
  })
}

const cancelEdit = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id }
  })
}

const saveChanges = async () => {
  loading.value = true
  try {
    // This would need an update partner API endpoint
    // For now, just navigate back to view mode
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call

    router.push({
      name: 'partner-details',
      params: { id: route.params.id }
    })
  } catch (error) {
    console.error('Error saving partner:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'partners' })
}

const manageServices = () => {
  router.push({
    name: 'partner-services',
    query: { partner_id: route.params.id }
  })
}

const manageSettings = () => {
  // Navigate to partner settings page or open modal
  console.log('Manage settings for partner:', route.params.id)
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '-'
  if (apiKey.length <= 8) return apiKey
  return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
}

// Additional methods for tabs
const viewAllBets = () => {
  router.push({
    name: 'partners-bets',
    query: { partner_id: route.params.id }
  })
}

const viewAllUsers = () => {
  // Navigate to partner users page when available
  console.log('View all users for partner:', route.params.id)
}

const handleViewService = (service: any) => {
  // Handle viewing individual service details
  console.log('View service details:', service)
  // Could navigate to service details page or open another modal
}

// Table row click handlers
const handleSettingsRowClick = (_settings: any) => {
  showSettingsModal.value = true
}

const handleServiceRowClick = (service: any) => {
  console.log('Service row clicked:', service)
  // Could navigate to service details or open modal
}

const handleBetRowClick = (bet: any) => {
  console.log('Bet row clicked:', bet)
  // Could navigate to bet details or open modal
}

const handleUserRowClick = (user: any) => {
  console.log('User row clicked:', user)
  // Could navigate to user details or open modal
}

// Status text helpers
const getServiceStatusText = (status: any) => {
  if (status === 'active' || status === 1) return 'Active'
  if (status === 'inactive' || status === 0) return 'Inactive'
  if (status === 'pending') return 'Pending'
  return 'Unknown'
}

const getUserStatusText = (status: any) => {
  if (status === 'active' || status === 1) return 'Active'
  if (status === 'inactive' || status === 0) return 'Inactive'
  if (status === 'pending') return 'Pending'
  return 'Unknown'
}

const parseWebsites = (websites: any) => {
  if (!websites) return []

  try {
    if (typeof websites === 'string') {
      return JSON.parse(websites)
    }
    return Array.isArray(websites) ? websites : []
  } catch {
    return []
  }
}

const fetchPartnerBets = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerBets({ partner_id: partnerId, limit: 10 })
    if (response.status === 200) {
      partnerBets.value = response.message.data || []
    } else {
      partnerBets.value = []
    }
  } catch (error) {
    console.error('Error fetching partner bets:', error)
    partnerBets.value = []
  }
}

const fetchPartnerUsers = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    // This would be implemented when the API endpoint is available
    // const response = await partnerApi.getPartnerUsers({ partner_id: partnerId, limit: 10 })
    // if (response.status === 200) {
    //   partnerUsers.value = response.message.data || []
    // } else {
    //   partnerUsers.value = []
    // }
    partnerUsers.value = [] // Placeholder until API is available
  } catch (error) {
    console.error('Error fetching partner users:', error)
    partnerUsers.value = []
  }
}

// Lifecycle
onMounted(async () => {
  console.log('PartnerDetails mounted, route params:', route.params)

  await Promise.all([
    fetchPartner(),
    fetchPartnerSettings(),
    fetchPartnerServices(),
    fetchPartnerBets(),
    fetchPartnerUsers()
  ])
  initialLoading.value = false

  console.log('Partner loading completed')
})
</script>
