<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Partners Bets</h1>
          <p class="mt-1 text-sm text-gray-600">
            Monitor and analyze betting activity across all partners
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="refreshData" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Bets</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalBets.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Volume</p>
            <p class="text-2xl font-semibold text-gray-900">${{ totalVolume.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Partners</p>
            <p class="text-2xl font-semibold text-gray-900">{{ activePartners }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Win Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ winRate }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="partner-filter" class="block text-sm font-medium text-gray-700 mb-2">Partner</label>
          <select id="partner-filter" v-model="filters.partner_id" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Partners</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Bet Status</label>
          <select id="status-filter" v-model="filters.bet_status" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="won">Won</option>
            <option value="lost">Lost</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div>
          <label for="sport-filter" class="block text-sm font-medium text-gray-700 mb-2">Sport</label>
          <select id="sport-filter" v-model="filters.sport_type" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Sports</option>
            <option value="football">Football</option>
            <option value="basketball">Basketball</option>
            <option value="tennis">Tennis</option>
            <option value="cricket">Cricket</option>
          </select>
        </div>
        <div>
          <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
          <select id="date-filter" v-model="filters.date_range" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Partners Bets Table -->
     <!-- Bets Tab -->
          <DataTable :data="partnersBets" :headers="betsHeaders" title="Partner Bets" :loading="loading"
            :searchable="true" :pagination="true" :current-page="1" :total-records="partnersBetsCount || 0"
            :page-size="10" :has-actions="true" empty-message="No bets found" @row-click="handleRowClick">
            <!-- Header Actions -->
            <template #header-actions>
              <!-- <button @click="viewAllBets"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <EyeIcon class="w-4 h-4 mr-2" />
                View All Bets
              </button> -->
            </template>

            <!-- Custom Bet Reference Cell -->
            <template #cell-bet_reference="{ value, item }">
              <div class="text-sm">
                <div class="font-medium text-gray-900">{{ value || 'n/a' }}</div>
                <div class="text-gray-500">ID: {{ item.bet_id }}</div>
              </div>
            </template>

            <!-- Custom Bet Amount Cell -->
            <template #cell-bet_amount="{ value }">
              <span class="font-medium text-gray-900">{{ formatCurrency(value) }}</span>
            </template>

            <!-- Custom Potential Win Cell -->
            <template #cell-possible_win="{ value }">
              <span class="font-medium text-green-600">{{ formatCurrency(value) }}</span>
            </template>

            <!-- BetType -->
            <template #cell-bet_type="{ value }">
              <span :class="{
                'bg-green-100 text-green-800': value === '1',
                'bg-red-100 text-red-800': value === '0',
                'bg-orange-100 text-orange-800': value === '3'
              }" class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium">
                {{ value === '1' ? 'Cash' : value === '0' ? 'Bonus' : value === '3' ? 'Free' : '-' }}
              </span>
            </template>

            <!-- Custom Status Cell -->
            <template #cell-status="{ value }">
              <span :class="{
                'bg-yellow-100 text-yellow-800': value === '0',
                'bg-green-100 text-green-800': value === '1',
                'bg-red-100 text-red-800': value === '2',
                'bg-gray-100 text-gray-800': value === '3'
              }" class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium capitalize">
                {{ value === '0' ? 'Pending' : value === '1' ? 'Won' : value === '2' ? 'Lost' : value === '3' ? 'Cancelled' : '-' }}
              </span>
            </template>

            <!-- Actions -->
            <template #actions="{ item, closeDropdown }">
              <button @click="viewBetDetails(item); closeDropdown()"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 transition-colors duration-200">
                <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                View Bet Details
              </button>
              <button @click="viewBetSlip(item); closeDropdown()"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 transition-colors duration-200">
                <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                View Bet Slip
              </button>
            </template>
          </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { formatDate, formatCurrency } from '@/utils/formatters'
import DataTable from '@/components/DataTable.vue'
import { partnerApi } from '@/services/partnerApi'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const partnersBets = ref<any[]>([])
const partnersBetsCount = ref(0)
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Filters
const filters = reactive({
  partner_id: '',
  bet_status: '',
  sport_type: '',
  date_range: ''
})

const betsHeaders = computed(() => ({
  bet_reference: 'Bet Reference',
  // bet_id: 'Bet ID',
  profile_id: 'Username',
  partner_name: 'Email',
  bet_amount: 'Bet Amount',
  possible_win: 'Possible Win',
  total_odd: 'Total Odds',
  total_games: 'Total Games',
  live_events: 'Live Events',
  bet_type: 'Bet Type',
  status: 'Status',
  created_at: 'Created'
}))


// Computed properties for summary cards
const totalBets = computed(() => partnersBets.value.length)
const totalVolume = computed(() =>
  partnersBets.value.reduce((sum, bet) => sum + (bet.bet_amount || 0), 0)
)
const activePartners = computed(() =>
  new Set(partnersBets.value.map(bet => bet.partner_id)).size
)
const winRate = computed(() => {
  const wonBets = partnersBets.value.filter(bet => bet.bet_status === 'won').length
  return totalBets.value > 0 ? Math.round((wonBets / totalBets.value) * 100) : 0
})

// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await partnerApi.getPartnerBets({
      page: currentPage.value,
      limit: pageSize.value,
      partner_id: filters.partner_id || route.query.partner_id as string || '',
      status: filters.bet_status,
      // sport: filters.sport_type,
      search: searchQuery.value
    })

    console.log('Partner bets responsexxxx:', JSON.stringify(response.message.data ))

    if (response.status === 200) {
      partnersBets.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load partner bets:', response)
      partnersBets.value = []
      totalRecords.value = 0
    }

    // Load partners for filter dropdown
    const partnersResponse = await partnerApi.getPartners({ limit: 100 })
    if (partnersResponse.status === 200) {
      partners.value = partnersResponse.message.data || []
    }

  } catch (error) {
    console.error('Error loading partners bets:', error)
  } finally {
    loading.value = false
  }
}



const fetchPartnerBets = async () => {
  const partnerId = route.params.id as string || ''
  if (!partnerId) return

  try {
    const response = await partnerApi.getPartnerBets({ partner_id: partnerId, limit: 10 })
    console.log('Partner Bets API Response: ', JSON.stringify(response))
    if (response.status === 200) {
      partnersBets.value = response.message.data || []
      partnersBetsCount.value = response.message.total || 0
    } else {
      partnersBets.value = []
    }
  } catch (error) {
    console.error('Error fetching partner bets:', error)
    partnersBets.value = []
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewBet(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}


const viewBet = (bet: any) => {
  console.log('View bet:', bet)
  // Implement view logic
  const payload = {
    bet_id: bet.bet_id,
    partner_id: bet.partner_id
  }
  router.push({ name: 'partners-bet-details', query: payload })
}

const viewBetSlip = (bet: any) => {
  console.log('View bet slip:', bet)
  router.push({ name: 'partners-bet-slips', query: { bet_id: bet.bet_id } })
}

const exportData = () => {
  console.log('Export partners bets data')
  // Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
