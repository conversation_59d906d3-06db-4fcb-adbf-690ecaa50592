/**
 * Simple navigation cache utility using sessionStorage
 * This provides a lightweight caching mechanism for navigation between list and detail pages
 */

export type CacheDataType = 'partner' | 'user' | 'role' | 'service' | 'permission' | 'setting'

interface CacheEntry {
  data: any
  timestamp: number
  type: CacheDataType
}

/**
 * Store data in sessionStorage with timestamp
 */
export const storeNavigationData = (data: any, type: CacheDataType): void => {
  try {
    const cacheEntry: CacheEntry = {
      data,
      timestamp: Date.now(),
      type
    }
    
    const key = `navigation_${type}`
    sessionStorage.setItem(key, JSON.stringify(cacheEntry))
    
    console.log(`Stored ${type} data for navigation:`, data.id || data.name)
  } catch (error) {
    console.error(`Error storing ${type} data:`, error)
  }
}

/**
 * Retrieve data from sessionStorage with validation
 */
export const getNavigationData = (type: CacheDataType, expectedId?: string | number): any => {
  try {
    const key = `navigation_${type}`
    const storedData = sessionStorage.getItem(key)
    
    if (!storedData) {
      console.log(`No cached ${type} data found`)
      return null
    }
    
    const cacheEntry: CacheEntry = JSON.parse(storedData)
    
    // Check if data is still fresh (within 30 minutes)
    const thirtyMinutes = 30 * 60 * 1000
    if (Date.now() - cacheEntry.timestamp > thirtyMinutes) {
      console.log(`Cached ${type} data expired, removing`)
      sessionStorage.removeItem(key)
      return null
    }
    
    // Validate type matches
    if (cacheEntry.type !== type) {
      console.log(`Cached data type mismatch: expected ${type}, got ${cacheEntry.type}`)
      return null
    }
    
    // If expectedId is provided, validate it matches
    if (expectedId !== undefined && cacheEntry.data.id !== undefined) {
      const cachedId = cacheEntry.data.id
      if (cachedId != expectedId && cachedId != parseInt(expectedId as string)) {
        console.log(`Cached ${type} ID mismatch: expected ${expectedId}, got ${cachedId}`)
        return null
      }
    }
    
    console.log(`Using cached ${type} data:`, cacheEntry.data.id || cacheEntry.data.name)
    return cacheEntry.data
  } catch (error) {
    console.error(`Error reading cached ${type} data:`, error)
    return null
  }
}

/**
 * Clear specific cached data
 */
export const clearNavigationData = (type: CacheDataType): void => {
  try {
    const key = `navigation_${type}`
    sessionStorage.removeItem(key)
    console.log(`Cleared cached ${type} data`)
  } catch (error) {
    console.error(`Error clearing cached ${type} data:`, error)
  }
}

/**
 * Clear all navigation cache data
 */
export const clearAllNavigationData = (): void => {
  try {
    const types: CacheDataType[] = ['partner', 'user', 'role', 'service', 'permission', 'setting']
    
    types.forEach(type => {
      const key = `navigation_${type}`
      sessionStorage.removeItem(key)
    })
    
    console.log('Cleared all navigation cache data')
  } catch (error) {
    console.error('Error clearing all navigation cache data:', error)
  }
}

/**
 * Navigate with data caching
 * This is a helper function that stores data and navigates in one call
 */
export const navigateWithCache = (
  router: any,
  data: any,
  type: CacheDataType,
  routeName: string,
  routeParams?: any,
  routeQuery?: any
): void => {
  // Store the data first
  storeNavigationData(data, type)
  
  // Build route configuration
  const routeConfig: any = { name: routeName }
  if (routeParams) routeConfig.params = routeParams
  if (routeQuery) routeConfig.query = routeQuery
  
  // Navigate
  router.push(routeConfig)
  
  console.log(`Navigated to ${routeName} with cached ${type} data`)
}

/**
 * Check if cached data exists and is valid
 */
export const hasCachedData = (type: CacheDataType, expectedId?: string | number): boolean => {
  const data = getNavigationData(type, expectedId)
  return data !== null
}

/**
 * Get cache info for debugging
 */
export const getCacheInfo = (): Record<CacheDataType, any> => {
  const types: CacheDataType[] = ['partner', 'user', 'role', 'service', 'permission', 'setting']
  const info: Record<string, any> = {}
  
  types.forEach(type => {
    const key = `navigation_${type}`
    const storedData = sessionStorage.getItem(key)
    
    if (storedData) {
      try {
        const cacheEntry: CacheEntry = JSON.parse(storedData)
        const age = Date.now() - cacheEntry.timestamp
        const ageMinutes = Math.floor(age / (1000 * 60))
        
        info[type] = {
          id: cacheEntry.data.id || 'N/A',
          name: cacheEntry.data.name || 'N/A',
          ageMinutes,
          isExpired: age > 30 * 60 * 1000
        }
      } catch (error) {
        info[type] = { error: 'Invalid cache data' }
      }
    } else {
      info[type] = null
    }
  })
  
  return info
}
